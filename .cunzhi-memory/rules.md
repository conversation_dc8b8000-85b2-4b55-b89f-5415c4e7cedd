# 开发规范和规则

- 项目已成功迁移到Tailwind CSS 4 + Vite插件配置，使用@import "tailwindcss"语法，animejs已升级到4.0.2版本
- 移动端优先布局规范：容器间距px-3 py-4，输入框高度h-12，按钮最小44px触控区域，网格布局grid-cols-1 sm:grid-cols-2，文字大小text-base sm:text-lg响应式
- DaisyUI 5最佳实践：使用shadow-sm替代shadow-2xl，移除input-bordered（默认有边框），页面背景使用bg-base-200，避免重复标题
- CSS样式冲突解决方案：移除自定义input、steps、progress样式，使用DaisyUI原生样式，通过input-ghost类移除边框，避免自定义CSS覆盖DaisyUI组件
- 进度条和边框完全修复：使用DaisyUI原生progress元素替代自定义HTML，单位标签使用btn-ghost移除边框，彻底清理所有自定义btn样式避免DaisyUI冲突
- Setup页面优化完成：移除冗余进度条，实现全屏布局，目标设置字段顺序调整为天数在前体重在后，添加智能推荐体重功能，统一单位标签样式，优化布局间距
- Setup页面交互优化完成：修复推荐按钮高度匹配，集成活动水平到推荐算法，应用2024年现代化配色方案（emerald-indigo渐变），推荐按钮需要目标天数+活动水平才启用
- Setup页面最终优化完成：统一emerald配色边框系统，调整交互流程为目标天数→活动水平→目标体重，重新设计基本信息页面为卡片式布局，应用渐变标题和现代化视觉效果
- Setup页面移动端优先重新设计完成：以390x844为标准，应用2025年indigo-purple配色方案，修复输入框黑色边框问题，移除max-w限制实现全屏布局，垂直布局优化触控体验
- Setup页面推荐算法与确认页面优化完成：修复推荐算法验证问题，放宽体重限制至20-500kg，优化确认信息页面为移动端卡片式布局，推荐算法与验证逻辑保持一致
- Setup页面最终UI优化完成：导航按钮专业化设计，桌面端充分利用屏幕宽度（max-w-4xl/5xl/6xl），活动水平默认选择中度活动，确认页面4列网格布局，响应式间距优化
- Setup页面最终优化完成：移除活动水平默认值，修复桌面端目标天数宽度对齐（max-w-xl），集成Anime.js动画效果包括步骤切换、按钮点击、输入框聚焦、错误提示、推荐结果显示等动画
- Setup页面宽度对齐优化完成：目标天数输入框、活动水平按钮、目标体重输入框统一使用max-w-md lg:max-w-lg xl:max-w-xl宽度限制，实现完美视觉对齐
- 移动端充满屏幕布局优化：外层容器px-0 py-0移除边距，主内容卡片rounded-none shadow-none border-0移除装饰，各区域使用px-4 sm:px-0确保移动端内容有适当间距但充满屏幕
- Setup页面姓名输入框宽度对齐修复：移除max-w限制使其与性别按钮组宽度匹配；Dashboard页面UI全面现代化：使用DaisyUI 5.0.46组件，渐变背景，毛玻璃效果，现代化卡片设计，emoji图标，stats组件，badge组件，现代化按钮设计
- Dashboard页面移动端完全重构完成：基于Anime.js v4动画系统，移动端优先设计，单列布局，44px最小触控区域，毛玻璃效果，现代化卡片设计，响应式间距，完美的390px移动端适配
- Dashboard页面UI细节优化完成：统一三餐分配样式为badge-primary，所有数值格式化为整数（formatWeight、formatNutrient使用Math.round），移除冗余头部元素，创建现代化实时日期时间显示组件
- Dashboard页面卡片内部布局优化与UI美化重构完成：今日卡路里炫酷橙红渐变设计，营养详情绿色系数据可视化，三餐分配彩色进度条设计，代谢信息玫瑰色优化布局，个人档案天蓝绿色现代化设计，所有数值显示整数
- Dashboard页面UI细节修复与数据优化完成：简化个人档案移除进度条，统一时间日期紫色风格，营养建议琥珀色设计，修复营养计划卡路里限额，所有进度条基于真实数据计算，Math.round确保整数显示
- Dashboard页面环形图重叠显示修复与营养建议UI重构完成：移除环形图上重叠的完成度百分比显示，营养建议组件完全重构为现代化渐变卡片设计，emoji图标系统，毛玻璃效果，与整体风格统一
- Dashboard页面环形图移除与卡片设计统一优化完成：移除今日卡路里环形图，恢复个人档案👤图标，移除营养建议和每日卡路里限额的白色毛玻璃包装，统一卡片设计风格，直接展示渐变背景
- Dashboard页面营养建议卡片色彩优化与底部导航栏添加完成：营养建议内部卡片使用柔和的琥珀色系渐变，添加底部固定导航栏包含开始记录食物、查看日历、我的三个按钮，移动端优先设计，毛玻璃效果
- Dashboard页面营养建议UI重新设计与数据显示优化完成：营养建议采用简洁列表式设计无内部卡片，三餐分配显示实际摄入vs目标数据格式，所有进度条基于真实数据计算，底部导航栏固定显示确认
- Dashboard页面营养建议合并到营养详情与DaisyUI dock导航栏重构完成：营养建议合并到营养详情中显示简洁提示，移除独立营养建议卡片，使用DaisyUI dock组件重构底部导航栏，dock-active状态管理，dock-label标签
- Dashboard页面营养建议样式修正完成：恢复营养详情原有的毛玻璃卡片样式，保持bg-white/60 backdrop-blur-sm rounded-xl设计，DaisyUI dock导航栏确认固定显示在页面底部可见区域
- 固定底部导航栏全面屏适配完成：使用fixed-bottom-nav自定义CSS类，env(safe-area-inset-bottom)适配iPhone和Android全面屏手势区，backdrop-filter毛玻璃效果，touch-manipulation触控优化，pb-safe安全区域适配
- AURA-X协议严格执行完成：Context7-MCP研究底部导航栏最佳实践，寸止MCP确认技术方案，完全重构固定底部导航栏，移除时间卡片，基于Tailwind CSS和Capacitor最佳实践，env(safe-area-inset-bottom)全面屏适配，position:fixed真正固定在视口底部
- Setup页面黑色边框修复与底部导航栏定位问题解决完成：添加全局边框重置(*{border:none!important})，底部导航栏使用position:fixed!important和z-index:9999!important，PageTransition组件移除transform-gpu类避免影响fixed定位，动画完成后移除transform
- BottomNavigation组件导入错误修复与Setup页面输入框黑色边框问题解决完成：重新创建BottomNavigation组件和导出文件，修复CSS中input和input-bordered类的边框样式，使用var(--border-color)替代默认黑色边框，确保输入框样式正确
- 底部导航栏完全重构完成：删除React Portal和原生DOM操作，基于Context7最佳实践使用简洁的fixed定位，使用Tailwind CSS类替代内联样式，组件从194行简化到85行，使用声明式React组件树渲染
- 移动端固定底部导航栏完全重新实现完成：基于Context7最佳实践，使用max(env(safe-area-inset-bottom), 8px)安全区域适配，44px最小触控标准，原生App级别触控反馈，真正的固定定位，拇指友好设计，无障碍支持
- 桌面端数字输入优化与底部导航栏深度分析完成：移除数字输入步进器控件，统一所有单位按钮蓝色渐变配色，修复底部导航栏按钮尺寸一致性，统一hover:scale-105效果，确保原生App级别体验
- 推荐功能优化完成：恢复目标天数和活动水平的智能计算，但放宽减重速度限制，活动系数0.8-1.6，基础每周减重0.5kg，超重至少减2kg，健康范围最多减5kg，确保推荐在健康范围内且不超过当前体重
- 底部导航栏滑动隐藏功能实现完成：添加useState和useEffect管理可视性状态，监听scroll事件，向下滚动超过100px且滚动距离超过10px时隐藏导航栏，向上滚动时显示，使用translate-y-full和transition-transform实现平滑动画效果
- 底部导航栏层级冲突问题修复完成：使用z-index:2147483647(32位整数最大值)确保绝对最高层级，添加!important强制优先级，全局CSS中添加nav[style*="2147483647"]选择器保障，action-button限制为z-index:1000，确保导航栏始终在所有页面元素之上
- 底部导航栏层叠上下文问题彻底解决：发现Anime.js的transform属性创建新层叠上下文导致z-index失效，通过将BottomNavigation组件移到根级别（脱离transform影响范围）解决，同时为CalendarPage添加BottomNavigation组件确保跨页面一致性，使用z-index:2147483647确保绝对最高层级
- AURA-X协议集成完成：所有AI决策通过寸止MCP确认，营养分析使用context7-mcp获取权威知识，食物识别确保用户控制，健康建议基于最新标准，严格遵循AI绝不自作主张原则
- 数字格式化规则：个人档案数据（体重、身高、年龄等）保持两位小数显示，健康数据（BMI、目标体重等）保持两位小数显示，其他所有数值（卡路里、营养数据等）显示为整数
- 用户要求修复多个UI问题：1.设置日期显示和数据同步问题 2.记录页面食物名字过长的UI挤占问题 3.分析过程中禁止触摸滑动只允许点击终止识别 4.营养详情基础信息记录日期文字大小同步问题
- 用户要求修复食物识别功能的4个问题：1.统一终止识别图标样式 2.修复识别成功后确认添加按钮无法点击问题 3.调整日期选择时机到识别开始前 4.严格使用寸止协议进行所有决策确认
- 用户报告了两个问题：1.FoodRecognitionModal提交过程中发生错误，Cannot read properties of undefined (reading 'calories') 2.当前添加按钮不能居中的问题需要修复
- 用户要求修复3个问题：1.Setup页面模型管理功能异常，输入密码xiamu后无反应 2.Gemini图片识别API请求错误，API响应格式错误没有文本内容 3.提示词统一管理重构，创建通用提示词库。需要使用context7-mcp查找Gemini API最佳实践
- AI提供商重构：移除夏目提供商，重构Gemini使用x-goog-api-key认证，创建OpenAI提供商使用Bearer认证，统一图片处理和AI模型选择，严格按照官方curl格式实现API请求
- AI模型管理恢复官方模型选项：在AIModelManagementModal中添加handleAddOfficialModel函数，提供预配置的Gemini官方模型选项，用户可选择"官方模型"或"自定义模型"，保持统一AI提供商架构
- AI模型管理系统优化：1.官方模型使用OpenAI格式(apiv2.aliyahzombie.top)，密码保护(xiamu)；2.模态框高度优化减少空白；3.视觉模型添加眼睛图标标识；4.API密钥查看需密码验证；5.进程管理确保单一开发服务器
