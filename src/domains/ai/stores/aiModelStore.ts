import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  AIModelConfig, 
  AIModelStore, 
  AIModelValidationResult, 
  AIModelProvider,
  OpenAIModelInfo,
  GeminiModelInfo
} from '@/shared/types/aiModel';

export const useAIModelStore = create<AIModelStore>()(
  persist(
    (set, get) => ({
      models: [],
      activeModelId: null,

      addModel: (config) => {
        const newModel: AIModelConfig = {
          ...config,
          id: crypto.randomUUID(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        set((state) => ({
          models: [...state.models, newModel],
          // 如果是第一个模型，自动设为活跃
          activeModelId: state.models.length === 0 ? newModel.id : state.activeModelId,
        }));
      },

      updateModel: (id, updates) => {
        set((state) => ({
          models: state.models.map((model) =>
            model.id === id
              ? { ...model, ...updates, updatedAt: new Date().toISOString() }
              : model
          ),
        }));
      },

      deleteModel: (id) => {
        set((state) => ({
          models: state.models.filter((model) => model.id !== id),
          activeModelId: state.activeModelId === id ? null : state.activeModelId,
        }));
      },

      setActiveModel: (id) => {
        set({ activeModelId: id });
      },

      getActiveModel: () => {
        const state = get();
        return state.models.find((model) => model.id === state.activeModelId) || null;
      },

      validateModel: async (config) => {
        try {
          if (!config.provider || !config.apiKey || !config.baseUrl) {
            return {
              isValid: false,
              error: '请填写完整的配置信息',
            };
          }

          if (config.provider === 'openai') {
            return await validateOpenAIModel(config);
          } else if (config.provider === 'gemini') {
            return await validateGeminiModel(config);
          }

          return {
            isValid: false,
            error: '不支持的模型提供商',
          };
        } catch (error) {
          return {
            isValid: false,
            error: error instanceof Error ? error.message : '验证失败',
          };
        }
      },

      getModelList: async (provider, apiKey, baseUrl) => {
        try {
          if (provider === 'openai') {
            return await getOpenAIModelList(apiKey, baseUrl);
          } else if (provider === 'gemini') {
            return await getGeminiModelList(apiKey, baseUrl);
          }
          return [];
        } catch (error) {
          console.error('获取模型列表失败:', error);
          return [];
        }
      },
    }),
    {
      name: 'ai-model-store',
      version: 1,
    }
  )
);

// OpenAI模型验证
async function validateOpenAIModel(config: Partial<AIModelConfig>): Promise<AIModelValidationResult> {
  try {
    const response = await fetch(`${config.baseUrl}/v1/models`, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const models = data.data as OpenAIModelInfo[];
    
    // 检查指定的模型是否存在
    const modelExists = config.modelName ? 
      models.some(model => model.id === config.modelName) : 
      true;

    if (!modelExists) {
      return {
        isValid: false,
        error: `模型 ${config.modelName} 不存在`,
        modelList: models.map(m => m.id),
      };
    }

    // 检查视觉支持（基于模型名称判断）
    const supportsVision = config.modelName ? 
      config.modelName.includes('vision') || config.modelName.includes('gpt-4') : 
      false;

    return {
      isValid: true,
      capabilities: {
        supportsText: true,
        supportsVision,
        supportsAudio: false,
      },
      modelList: models.map(m => m.id),
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'OpenAI API验证失败',
    };
  }
}

// Gemini模型验证
async function validateGeminiModel(config: Partial<AIModelConfig>): Promise<AIModelValidationResult> {
  try {
    const response = await fetch(`${config.baseUrl}/v1beta/models`, {
      headers: {
        'x-goog-api-key': config.apiKey!,
        'Content-Type': 'application/json',
      },
    });
    // {{ AURA-X: Modify - 使用官方x-goog-api-key认证和v1beta端点. Approval: 寸止(ID:1736937800). }}

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const models = data.models as GeminiModelInfo[];
    
    // 检查指定的模型是否存在
    const modelExists = config.modelName ? 
      models.some(model => model.name.includes(config.modelName!)) : 
      true;

    if (!modelExists) {
      return {
        isValid: false,
        error: `模型 ${config.modelName} 不存在`,
        modelList: models.map(m => m.name.split('/').pop() || m.name),
      };
    }

    // 检查视觉支持（基于模型名称和支持的生成方法判断）
    const targetModel = models.find(model => 
      config.modelName ? model.name.includes(config.modelName) : true
    );
    
    const supportsVision = targetModel ? 
      targetModel.name.includes('vision') || 
      targetModel.supportedGenerationMethods.includes('generateContent') :
      false;

    return {
      isValid: true,
      capabilities: {
        supportsText: true,
        supportsVision,
        supportsAudio: false,
      },
      modelList: models.map(m => m.name.split('/').pop() || m.name),
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Gemini API验证失败',
    };
  }
}

// 获取OpenAI模型列表
async function getOpenAIModelList(apiKey: string, baseUrl: string): Promise<string[]> {
  const response = await fetch(`${baseUrl}/v1/models`, {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`获取OpenAI模型列表失败: ${response.status}`);
  }

  const data = await response.json();
  return (data.data as OpenAIModelInfo[]).map(model => model.id);
}

// 获取Gemini模型列表
async function getGeminiModelList(apiKey: string, baseUrl: string): Promise<string[]> {
  const response = await fetch(`${baseUrl}/v1/models?key=${apiKey}`);

  if (!response.ok) {
    throw new Error(`获取Gemini模型列表失败: ${response.status}`);
  }

  const data = await response.json();
  return (data.models as GeminiModelInfo[]).map(model => 
    model.name.split('/').pop() || model.name
  );
}
