/**
 * AI提示词统一管理系统
 * 支持不同场景的提示词配置和管理
 */

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables?: string[];
  category: PromptCategory;
  version: string;
  createdAt: Date;
  updatedAt: Date;
}

export type PromptCategory = 
  | 'food_recognition'
  | 'nutrition_analysis' 
  | 'text_analysis'
  | 'health_advice'
  | 'exercise_suggestion';

export interface PromptContext {
  additionalContext?: string;
  userContext?: any;
  imageCount?: number;
  method?: 'image' | 'text';
  [key: string]: any;
}

/**
 * 提示词管理器类
 */
export class PromptManager {
  private static instance: PromptManager;
  private prompts: Map<string, PromptTemplate> = new Map();

  private constructor() {
    this.initializeDefaultPrompts();
  }

  public static getInstance(): PromptManager {
    if (!PromptManager.instance) {
      PromptManager.instance = new PromptManager();
    }
    return PromptManager.instance;
  }

  /**
   * 初始化默认提示词
   */
  private initializeDefaultPrompts(): void {
    // 统一图片食物识别提示词（支持单图片和多图片）
    this.prompts.set('unified_image_food_recognition', {
      id: 'unified_image_food_recognition',
      name: '统一图片食物识别',
      description: '用于识别单张或多张图片中的食物信息',
      category: 'food_recognition',
      version: '3.0.0',
      template: `你是一个专业的营养师和食物识别专家。请仔细分析{{imageCount}}张食物图片，识别其中的所有食物并提供详细的营养信息。

**核心任务：**
1. **精确识别**：识别图片中的所有食物，包括主食、配菜、调料等
2. **重量估算**：基于视觉线索（餐具大小、食物比例等）估算每种食物的重量
3. **营养分析**：提供准确的营养成分数据，优先使用营养标签信息
4. **数据来源标注**：明确标注营养数据的来源（营养标签/视觉估算）
{{#if multiImage}}5. **跨图片分析**：分析所有图片，识别不同角度或状态的同一食物，避免重复计算{{/if}}

**输出格式要求：**
严格按照以下JSON格式输出，不要添加任何markdown标记或额外文字：

{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation",
      {{#if multiImage}}"imageIndex": 主要出现的图片索引(0开始),{{/if}}
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": true/false,
        "confidence": 置信度(0-1),
        "readableText": "标签文字内容"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体",
        "confidenceLevel": "high" | "medium" | "low"
      }
    }
  ],
  "analysisMetadata": {
    "hasNutritionLabel": true/false,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "visual_estimation",
    "processingNotes": "处理说明"
  }{{#if multiImage}},
  "multiImageAnalysis": {
    "totalImages": {{imageCount}},
    "duplicatesFound": 去重数量,
    "crossReferenceNotes": "跨图片分析说明"
  }{{/if}}
}

{{additionalContext}}`,
      variables: ['imageCount', 'multiImage', 'additionalContext'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // {{ AURA-X: Add - 创建统一图片识别提示词模板. Approval: 寸止(ID:1736937900). }}

    // 单图片食物识别提示词（保留向后兼容）
    this.prompts.set('single_image_food_recognition', {
      id: 'single_image_food_recognition',
      name: '单图片食物识别',
      description: '用于识别单张图片中的食物信息',
      category: 'food_recognition',
      version: '2.0.0',
      template: `你是一个专业的营养师和食物识别专家。请仔细分析这张食物图片，识别其中的所有食物并提供详细的营养信息。

**核心任务：**
1. **精确识别**：识别图片中的所有食物，包括主食、配菜、调料等
2. **重量估算**：基于视觉线索（餐具大小、食物比例等）估算每种食物的重量
3. **营养分析**：提供准确的营养成分数据，优先使用营养标签信息
4. **数据来源标注**：明确标注营养数据的来源（营养标签/视觉估算）

**输出格式要求：**
严格按照以下JSON格式输出，不要添加任何markdown标记或额外文字：

{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": true/false,
        "confidence": 置信度(0-1),
        "readableText": "标签文字内容"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体",
        "confidenceLevel": "high" | "medium" | "low"
      }
    }
  ],
  "analysisMetadata": {
    "hasNutritionLabel": true/false,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "visual_estimation",
    "processingNotes": "处理说明"
  }
}

**关键处理规则：**
• **营养标签优先**：如发现营养标签，优先使用标签数据而非估算
• **重量推理**：结合餐具、手部、包装等参考物估算重量
• **置信度评估**：基于图片清晰度、食物可见度、识别难度设定置信度
• **空图片处理**：如无食物，返回 {"foods": [], "analysisMetadata": {"imageQuality": "low", "recognitionMethod": "visual_estimation", "processingNotes": "未识别到食物"}}

**输出要求：直接返回JSON，无任何额外文字、标记或解释**

{{additionalContext}}`,
      variables: ['additionalContext'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 多图片食物识别提示词
    this.prompts.set('multi_image_food_recognition', {
      id: 'multi_image_food_recognition',
      name: '多图片食物识别',
      description: '用于识别多张图片中的食物信息',
      category: 'food_recognition',
      version: '2.0.0',
      template: `你是一个专业的营养师和食物识别专家。请仔细分析这{{imageCount}}张食物图片，识别其中的所有食物并提供详细的营养信息。

**核心任务：**
1. **跨图片分析**：分析所有图片，识别不同角度或状态的同一食物
2. **去重处理**：避免重复计算同一食物
3. **综合评估**：结合多个视角提供更准确的重量和营养估算
4. **图片关联**：标注每种食物出现在哪些图片中

**输出格式要求：**
严格按照以下JSON格式输出，不要添加任何markdown标记或额外文字：

{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation",
      "imageIndex": 主要出现的图片索引(0开始),
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": true/false,
        "confidence": 置信度(0-1),
        "readableText": "标签文字内容"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体",
        "confidenceLevel": "high" | "medium" | "low"
      }
    }
  ],
  "analysisMetadata": {
    "hasNutritionLabel": true/false,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "visual_estimation",
    "processingNotes": "处理说明"
  },
  "multiImageAnalysis": {
    "totalImages": {{imageCount}},
    "duplicatesFound": 去重数量,
    "crossReferenceNotes": "跨图片分析说明"
  }
}

**关键处理规则：**
• **智能去重**：识别不同图片中的同一食物，避免重复计算
• **多角度融合**：利用多个视角提供更准确的重量估算
• **标签优先**：任何图片中发现营养标签都优先使用
• **图片索引**：为每种食物标注主要出现的图片索引

**输出要求：直接返回JSON，无任何额外文字、标记或解释**

{{additionalContext}}`,
      variables: ['imageCount', 'additionalContext'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 文本食物分析提示词
    this.prompts.set('text_food_analysis', {
      id: 'text_food_analysis',
      name: '文本食物分析',
      description: '用于分析文本中的食物信息',
      category: 'text_analysis',
      version: '2.0.0',
      template: `你是一个专业的营养师和食物分析专家。请仔细分析用户输入的文字内容，识别其中提到的所有食物并提供详细的营养信息。

**核心任务：**
1. **文本解析**：从自然语言中提取食物信息
2. **量化推理**：将模糊描述转换为具体重量和营养数据
3. **上下文理解**：考虑用餐场景和食物搭配的合理性
4. **智能补全**：基于常见搭配补充可能遗漏的食物

**输出格式要求：**
严格按照以下JSON格式输出，不要添加任何markdown标记或额外文字：

{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "text_analysis",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "textAnalysis": {
        "originalText": "原始描述",
        "inferredWeight": "重量推理依据",
        "contextClues": "上下文线索"
      }
    }
  ],
  "analysisMetadata": {
    "textQuality": "high" | "medium" | "low",
    "ambiguityLevel": "low" | "medium" | "high",
    "extractedFoodCount": 提取的食物数量,
    "processingNotes": "处理说明"
  }
}

**关键处理规则：**
• **空文本检测**：如文字无食物信息，返回 {"foods": [], "analysisMetadata": {"textQuality": "low", "ambiguityLevel": "high", "extractedFoodCount": 0, "processingNotes": "未识别到明确的食物信息"}}
• **量化推理**：将模糊描述转换为具体重量（如"一碗米饭"→150g）
• **营养调整**：根据烹饪方式调整营养成分（如炒菜增加油脂）
• **置信度评估**：基于描述的明确程度和常见程度设定置信度
• **上下文理解**：考虑食物搭配和用餐场景的合理性

**输出要求：直接返回JSON，无任何额外文字、标记或解释**`,
      variables: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 营养建议提示词
    this.prompts.set('nutrition_advice', {
      id: 'nutrition_advice',
      name: '营养建议分析',
      description: '基于用户数据提供个性化营养建议',
      category: 'nutrition_analysis',
      version: '1.0.0',
      template: `你是一位专业的营养师。请基于用户的个人信息和当前饮食记录，提供个性化的营养建议。

用户信息：
- 年龄：{{age}}岁
- 性别：{{gender}}
- 身高：{{height}}cm
- 体重：{{weight}}kg
- 目标体重：{{targetWeight}}kg
- 活动水平：{{activityLevel}}
- 每日卡路里目标：{{dailyCalorieLimit}}卡

当前饮食记录：
{{nutritionData}}

请提供简洁实用的营养建议，包括：
1. 当前饮食评估
2. 营养平衡建议
3. 具体改进措施
4. 健康提醒

建议应该实用、具体、易于执行。`,
      variables: ['age', 'gender', 'height', 'weight', 'targetWeight', 'activityLevel', 'dailyCalorieLimit', 'nutritionData'],
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  /**
   * 添加提示词模板
   */
  public addPrompt(prompt: Omit<PromptTemplate, 'createdAt' | 'updatedAt'>): void {
    const fullPrompt: PromptTemplate = {
      ...prompt,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.prompts.set(prompt.id, fullPrompt);
  }

  /**
   * 获取提示词模板
   */
  public getPrompt(id: string): PromptTemplate | undefined {
    return this.prompts.get(id);
  }

  /**
   * 渲染提示词（替换变量和条件渲染）
   */
  public renderPrompt(id: string, context: PromptContext = {}): string {
    const prompt = this.getPrompt(id);
    if (!prompt) {
      throw new Error(`提示词模板不存在: ${id}`);
    }

    let rendered = prompt.template;

    // 处理条件渲染 {{#if condition}}...{{/if}}
    rendered = rendered.replace(/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (_, condition, content) => {
      return context[condition] ? content : '';
    });

    // 替换变量
    if (prompt.variables) {
      prompt.variables.forEach(variable => {
        const value = context[variable] || '';
        const placeholder = `{{${variable}}}`;
        rendered = rendered.replace(new RegExp(placeholder, 'g'), String(value));
      });
    }

    // 处理条件性内容
    if (context.additionalContext) {
      rendered = rendered.replace('{{additionalContext}}', `\n\n**补充信息：**\n${context.additionalContext}`);
    } else {
      rendered = rendered.replace('{{additionalContext}}', '');
    }

    return rendered.trim();
  }
  // {{ AURA-X: Modify - 增强提示词渲染支持条件渲染. Approval: 寸止(ID:1736937900). }}

  /**
   * 获取所有提示词
   */
  public getAllPrompts(): PromptTemplate[] {
    return Array.from(this.prompts.values());
  }

  /**
   * 按类别获取提示词
   */
  public getPromptsByCategory(category: PromptCategory): PromptTemplate[] {
    return this.getAllPrompts().filter(prompt => prompt.category === category);
  }

  /**
   * 更新提示词模板
   */
  public updatePrompt(id: string, updates: Partial<PromptTemplate>): void {
    const existing = this.prompts.get(id);
    if (!existing) {
      throw new Error(`提示词模板不存在: ${id}`);
    }

    const updated: PromptTemplate = {
      ...existing,
      ...updates,
      updatedAt: new Date()
    };

    this.prompts.set(id, updated);
  }

  /**
   * 删除提示词模板
   */
  public deletePrompt(id: string): boolean {
    return this.prompts.delete(id);
  }
}

// 导出单例实例
export const promptManager = PromptManager.getInstance();
