/**
 * AI模型管理相关类型定义
 */

export type AIModelProvider = 'openai' | 'gemini';
// {{ AURA-X: Modify - 移除夏目提供商类型. Approval: 寸止(ID:**********). }}

export interface AIModelConfig {
  id: string;
  name: string;
  provider: AIModelProvider;
  apiKey: string;
  baseUrl: string;
  modelName: string;
  supportsVision: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AIModelCapabilities {
  supportsText: boolean;
  supportsVision: boolean;
  supportsAudio: boolean;
  maxTokens?: number;
  supportedFormats?: string[];
}

export interface AIModelValidationResult {
  isValid: boolean;
  capabilities?: AIModelCapabilities;
  error?: string;
  modelList?: string[];
}

export interface OpenAIModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

export interface GeminiModelInfo {
  name: string;
  displayName: string;
  description: string;
  inputTokenLimit: number;
  outputTokenLimit: number;
  supportedGenerationMethods: string[];
}

export interface AIModelStore {
  models: AIModelConfig[];
  activeModelId: string | null;
  addModel: (config: Omit<AIModelConfig, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateModel: (id: string, updates: Partial<AIModelConfig>) => void;
  deleteModel: (id: string) => void;
  setActiveModel: (id: string) => void;
  getActiveModel: () => AIModelConfig | null;
  validateModel: (config: Partial<AIModelConfig>) => Promise<AIModelValidationResult>;
  getModelList: (provider: AIModelProvider, apiKey: string, baseUrl: string) => Promise<string[]>;
}
