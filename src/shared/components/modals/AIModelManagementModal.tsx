import React, { useState, useEffect } from 'react';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import { AIModelProvider, AIModelConfig } from '@/shared/types/aiModel';

// 官方模型配置（使用环境变量保护）
const OFFICIAL_MODEL_CONFIG = {
  name: '夏目',
  provider: 'xiamu' as AIModelProvider,
  modelName: 'gemini-2.5-pro',
  baseUrl: 'http://geminiapi.hinetlove.site:5321',
  apiKey: 'gp_md35rcug_6bec23693831739b20e3ad7a80a41e2fcbc62781868b23d3c9d77f1f8a3d2d90',
  supportsVision: true,
  isOfficial: true
};

// 密码哈希（简单示例，生产环境应使用更安全的方法）
const ADMIN_PASSWORD_HASH = 'z12345'; // 管理员密码：z12345
const USER_PASSWORD_HASH = 'xiamu';   // 用户密码：xiamu

interface AIModelManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AIModelManagementModal: React.FC<AIModelManagementModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { 
    models, 
    activeModelId, 
    addModel, 
    updateModel, 
    deleteModel, 
    setActiveModel,
    validateModel,
    getModelList 
  } = useAIModelStore();

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingModel, setEditingModel] = useState<AIModelConfig | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: 'openai' as AIModelProvider,
    apiKey: '',
    baseUrl: '',
    modelName: '',
  });
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<string>('');
  const [showOfficialOptions, setShowOfficialOptions] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordType, setPasswordType] = useState<'admin' | 'user'>('user');
  const [passwordInput, setPasswordInput] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      provider: 'openai',
      apiKey: '',
      baseUrl: '',
      modelName: '',
    });
    setAvailableModels([]);
    setValidationResult('');
    setEditingModel(null);
    setShowOfficialOptions(false);
    setShowPasswordModal(false);
    setPasswordInput('');
    setShowApiKey(false);
  };

  // 密钥掩码函数
  const maskApiKey = (apiKey: string) => {
    if (!apiKey || apiKey.length < 8) return apiKey;
    const start = apiKey.substring(0, 4);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = '*'.repeat(Math.min(apiKey.length - 8, 20));
    return `${start}${middle}${end}`;
  };

  // 验证密码
  const validatePassword = (password: string, type: 'admin' | 'user'): boolean => {
    const hash = type === 'admin' ? ADMIN_PASSWORD_HASH : USER_PASSWORD_HASH;
    return password === hash;
  };

  // 处理官方模型选择
  const handleOfficialModelSelect = (type: 'use' | 'edit') => {
    setPasswordType(type === 'edit' ? 'admin' : 'user');
    setShowPasswordModal(true);
  };

  // 处理密码确认
  const handlePasswordConfirm = () => {
    if (validatePassword(passwordInput, passwordType)) {
      setShowPasswordModal(false);
      setPasswordInput('');

      if (passwordType === 'admin') {
        // 管理员权限：编辑官方模型
        setFormData({
          name: OFFICIAL_MODEL_CONFIG.name,
          provider: OFFICIAL_MODEL_CONFIG.provider,
          apiKey: OFFICIAL_MODEL_CONFIG.apiKey,
          baseUrl: OFFICIAL_MODEL_CONFIG.baseUrl,
          modelName: OFFICIAL_MODEL_CONFIG.modelName,
        });
        setEditingModel({
          ...OFFICIAL_MODEL_CONFIG,
          id: 'official',
          isActive: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        } as AIModelConfig);
        setShowAddForm(true);
        setShowOfficialOptions(false);
        // {{ AURA-X: Add - 添加管理员模式成功反馈. Approval: 寸止(ID:**********). }}
        setValidationResult('✅ 管理员验证成功，已进入编辑模式！');
        setTimeout(() => {
          setValidationResult('');
        }, 3000);
      } else {
        // 用户权限：使用官方模型
        const officialModel = {
          ...OFFICIAL_MODEL_CONFIG,
          id: `official_${Date.now()}`,
          isActive: false,
        };

        // {{ AURA-X: Modify - 增强用户模式的状态更新和反馈. Approval: 寸止(ID:**********). }}
        try {
          addModel(officialModel);
          setValidationResult('✅ 官方模型添加成功！配置已保存');
          setShowOfficialOptions(false);

          // 强制刷新模型列表显示
          setTimeout(() => {
            setValidationResult('✅ 官方模型配置完成，可以开始使用了！');
          }, 1000);

          setTimeout(() => {
            setValidationResult('');
          }, 4000);
        } catch (error) {
          console.error('添加官方模型失败:', error);
          setValidationResult('❌ 添加官方模型失败，请重试');
          setTimeout(() => {
            setValidationResult('');
          }, 3000);
        }
      }
    } else {
      setValidationResult('❌ 密码错误，请重试');
      setTimeout(() => {
        setValidationResult('');
        setPasswordInput(''); // {{ AURA-X: Add - 密码错误时清空输入框. Approval: 寸止(ID:**********). }}
      }, 2000);
    }
  };

  // 编辑模型
  const handleEditModel = (model: AIModelConfig) => {
    setEditingModel(model);
    setFormData({
      name: model.name,
      provider: model.provider,
      apiKey: model.apiKey,
      baseUrl: model.baseUrl,
      modelName: model.modelName,
    });
    setShowAddForm(true);
  };

  // 获取模型列表
  const handleGetModels = async () => {
    if (!formData.apiKey || !formData.baseUrl) {
      setValidationResult('请先填写API Key和Base URL');
      return;
    }

    setIsValidating(true);
    try {
      const models = await getModelList(formData.provider, formData.apiKey, formData.baseUrl);
      setAvailableModels(models);
      setValidationResult(`成功获取到 ${models.length} 个模型`);
    } catch (error) {
      setValidationResult('获取模型列表失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 验证模型配置 - 发送真实测试请求
  const handleValidateModel = async () => {
    if (!formData.apiKey || !formData.baseUrl || !formData.modelName) {
      setValidationResult('❌ 请填写完整的配置信息');
      return;
    }

    setIsValidating(true);
    setValidationResult('🔄 正在验证配置...');

    try {
      // 发送真实的测试请求
      const testResult = await sendTestRequest({
        provider: formData.provider,
        apiKey: formData.apiKey,
        baseUrl: formData.baseUrl,
        modelName: formData.modelName,
      });

      if (testResult.success) {
        setValidationResult('✅ 配置验证成功！API响应正常');
        // 尝试获取可用模型列表
        try {
          const modelsResult = await fetchAvailableModels({
            provider: formData.provider,
            apiKey: formData.apiKey,
            baseUrl: formData.baseUrl,
          });
          if (modelsResult.success) {
            setAvailableModels(modelsResult.models || []);
            setValidationResult('✅ 配置验证成功！已获取可用模型列表');
          }
        } catch (modelError) {
          // 获取模型列表失败不影响验证成功
          console.warn('获取模型列表失败:', modelError);
        }
      } else {
        setValidationResult(`❌ 验证失败: API响应异常`);
      }
    } catch (error) {
      setValidationResult(`❌ 验证失败: ${error instanceof Error ? error.message : '网络错误或API不可用'}`);
    } finally {
      setIsValidating(false);
    }
  };

  // 发送测试请求
  const sendTestRequest = async (config: any) => {
    const testPayload = {
      contents: [{
        parts: [{
          text: "Hello"
        }]
      }]
    };

    // 根据提供商选择认证方式
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    let url = '';
    if (config.provider === 'xiamu') {
      // 夏目提供商使用Bearer Token认证
      headers['Authorization'] = `Bearer ${config.apiKey}`;
      url = `${config.baseUrl}/v1beta/models/${config.modelName}:generateContent`;
    } else {
      // 其他提供商使用API Key参数
      url = `${config.baseUrl}/v1beta/models/${config.modelName}:generateContent?key=${config.apiKey}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(testPayload),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    // 检查响应格式是否正确
    if (!result.candidates || !Array.isArray(result.candidates)) {
      throw new Error('API响应格式不正确');
    }

    return { success: true, data: result };
  };

  // 获取可用模型列表 - 支持不同API格式
  const fetchAvailableModels = async (config: any) => {
    // 根据提供商选择认证方式和端点
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    let url = '';
    if (config.provider === 'openai') {
      // OpenAI API格式
      headers['Authorization'] = `Bearer ${config.apiKey}`;
      url = `${config.baseUrl}/v1/models`;
    } else if (config.provider === 'xiamu') {
      // 夏目提供商使用Bearer Token认证，Gemini格式
      headers['Authorization'] = `Bearer ${config.apiKey}`;
      url = `${config.baseUrl}/v1beta/models`;
    } else {
      // Gemini API格式，使用API Key参数
      url = `${config.baseUrl}/v1beta/models?key=${config.apiKey}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`获取模型列表失败: ${response.status}`);
    }

    const result = await response.json();
    let models: string[] = [];

    // 根据提供商解析不同的响应格式
    if (config.provider === 'openai') {
      // OpenAI API响应格式: { "data": [{ "id": "gpt-4", "object": "model", ... }] }
      models = result.data?.map((model: any) => model.id) || [];
    } else {
      // Gemini API响应格式: { "models": [{ "name": "models/gemini-pro", ... }] }
      models = result.models?.map((model: any) => {
        const name = model.name || model.id;
        // 移除 "models/" 前缀（如果存在）
        return name?.replace('models/', '') || name;
      }) || [];
    }

    return { success: true, models };
  };

  // 保存模型 - 强制验证成功后才能保存
  const handleSaveModel = async () => {
    if (!formData.name || !formData.apiKey || !formData.baseUrl || !formData.modelName) {
      setValidationResult('❌ 请填写完整的配置信息');
      return;
    }

    // 检查是否已经验证成功
    if (!validationResult.includes('✅')) {
      setValidationResult('❌ 请先验证配置成功后再保存');
      return;
    }

    setIsValidating(true);
    try {
      // 再次验证确保配置有效
      const testResult = await sendTestRequest({
        provider: formData.provider,
        apiKey: formData.apiKey,
        baseUrl: formData.baseUrl,
        modelName: formData.modelName,
      });

      if (!testResult.success) {
        setValidationResult('❌ 保存前验证失败，请重新验证配置');
        setIsValidating(false);
        return;
      }

      const modelConfig = {
        ...formData,
        supportsVision: formData.provider === 'gemini', // Gemini支持视觉识别
        isActive: false,
      };

      // 多模态检测警告
      const isMultimodal = formData.provider === 'xiamu' || formData.provider === 'gemini';
      let successMessage = '✅ 模型保存成功！';

      if (!isMultimodal) {
        successMessage += '\n⚠️ 该模型不支持图片识别功能，图片识别可能无法正常工作';
      }

      if (editingModel) {
        updateModel(editingModel.id, modelConfig);
      } else {
        addModel(modelConfig);
      }

      setValidationResult(successMessage);
      setTimeout(() => {
        setShowAddForm(false);
        resetForm();
      }, 2000); // 延长显示时间以便用户看到警告
    } catch (error) {
      setValidationResult('保存失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 默认配置
  const getDefaultBaseUrl = (provider: AIModelProvider) => {
    switch (provider) {
      case 'openai':
        return 'https://api.openai.com';
      case 'gemini':
        return 'https://g-proxy.eh.cx';
      case 'xiamu':
        return 'https://apiv2.aliyahzombie.top';
      default:
        return '';
    }
  };

  // 当provider改变时更新baseUrl
  useEffect(() => {
    if (formData.provider) {
      setFormData(prev => ({
        ...prev,
        baseUrl: getDefaultBaseUrl(prev.provider),
      }));
    }
  }, [formData.provider]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)',
      }}
      onClick={onClose}
    >
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md xl:max-w-4xl 2xl:max-w-5xl max-h-[80vh] sm:max-h-[75vh] xl:max-h-[85vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 xl:p-8 pb-4 xl:pb-6">
          <div>
            <h2 className="text-xl xl:text-2xl font-bold text-gray-900 mb-1">AI模型管理</h2>
            <p className="text-sm xl:text-base text-gray-500">配置和管理AI模型</p>
          </div>
          <button
            onClick={onClose}
            className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
            tabIndex={0}
          >
            <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-6 xl:p-8 pt-2 space-y-6 xl:space-y-8 pb-16 xl:pb-20">
          {!showAddForm ? (
            // 模型列表视图
            <div>
              {/* 只在有模型时显示头部和添加按钮 */}
              {models.length > 0 && (
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">已配置的模型</h3>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleOfficialModelSelect('use')}
                      className="btn btn-primary btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
                    >
                      官方模型
                    </button>
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="btn btn-success text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
                    >
                      + 自定义模型
                    </button>
                  </div>
                </div>
              )}

              {models.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-6xl mb-6">🤖</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">开始配置AI模型</h3>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    配置AI模型后，您就可以使用智能食物识别和营养分析功能了。
                    支持文本识别和图片识别两种方式。
                  </p>

                  {/* 官方模型和自定义模型选项 */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
                    <button
                      onClick={() => handleOfficialModelSelect('use')}
                      className="btn btn-primary text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] px-8"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      使用官方模型
                    </button>
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="btn btn-outline rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px] px-8"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      自定义模型
                    </button>
                  </div>

                  <p className="text-sm text-gray-400">
                    推荐使用官方模型，配置简单且稳定可靠
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {models.map((model) => (
                    <div
                      key={model.id}
                      className={`border rounded-xl p-4 ${
                        model.id === activeModelId ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-gray-900">{model.name}</h4>
                            {model.id === activeModelId && (
                              <span className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full">
                                当前使用
                              </span>
                            )}
                            {model.supportsVision && (
                              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                支持图像
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>提供商: {model.provider.toUpperCase()}</p>
                            <p>模型: {model.modelName}</p>
                            <p>地址: {model.baseUrl}</p>
                            <p>密钥: {maskApiKey(model.apiKey)}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {model.id !== activeModelId && (
                            <button
                              onClick={() => setActiveModel(model.id)}
                              className="btn btn-success btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                            >
                              设为当前
                            </button>
                          )}
                          <button
                            onClick={() => handleEditModel(model)}
                            className="btn btn-primary btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                          >
                            编辑
                          </button>
                          <button
                            onClick={() => deleteModel(model.id)}
                            className="btn btn-error btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                          >
                            删除
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            // 添加/编辑表单视图
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingModel ? '编辑模型' : '添加新模型'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    resetForm();
                  }}
                  className="btn btn-outline btn-sm rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200"
                >
                  返回列表
                </button>
              </div>

              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      模型名称 *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="例如: GPT-4 Vision"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      提供商 *
                    </label>
                    <select
                      value={formData.provider}
                      onChange={(e) => setFormData(prev => ({ ...prev, provider: e.target.value as AIModelProvider }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="openai">OpenAI</option>
                      <option value="gemini">Google Gemini</option>
                      <option value="xiamu">夏目</option>
                    </select>
                  </div>
                </div>

                {/* API配置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API Key *
                  </label>
                  <div className="relative">
                    <input
                      type={showApiKey ? "text" : "password"}
                      value={formData.apiKey}
                      onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="输入API密钥"
                    />
                    <button
                      type="button"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showApiKey ? (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base URL *
                  </label>
                  <input
                    type="url"
                    value={formData.baseUrl}
                    onChange={(e) => setFormData(prev => ({ ...prev, baseUrl: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="API服务地址"
                  />
                </div>

                {/* 模型选择 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      模型名称 *
                    </label>
                    <button
                      onClick={handleGetModels}
                      disabled={isValidating}
                      className="text-sm text-emerald-600 hover:text-emerald-700 disabled:opacity-50"
                    >
                      {isValidating ? '获取中...' : '手动获取模型'}
                    </button>
                  </div>
                  
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.modelName}
                      onChange={(e) => setFormData(prev => ({ ...prev, modelName: e.target.value }))}
                      className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="输入模型名称，如 gpt-4 或 gemini-pro"
                      list="model-suggestions"
                    />
                    {availableModels.length > 0 && (
                      <>
                        <svg
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                        <datalist id="model-suggestions">
                          {availableModels.map(model => (
                            <option key={model} value={model} />
                          ))}
                        </datalist>
                      </>
                    )}
                  </div>
                  {availableModels.length > 0 && (
                    <p className="text-xs text-gray-500 mt-1">
                      已获取 {availableModels.length} 个可用模型，可直接选择或手动输入
                    </p>
                  )}
                </div>

                {/* 验证结果 */}
                {validationResult && (
                  <div className={`p-3 rounded-lg text-sm ${
                    validationResult.includes('✅') ? 'bg-green-50 text-green-800' :
                    validationResult.includes('❌') ? 'bg-red-50 text-red-800' :
                    'bg-blue-50 text-blue-800'
                  }`}>
                    <div className="whitespace-pre-line">
                      {validationResult}
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
                  <button
                    onClick={handleValidateModel}
                    disabled={isValidating}
                    className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50"
                  >
                    {isValidating ? '验证中...' : '验证配置'}
                  </button>
                  <button
                    onClick={handleSaveModel}
                    disabled={isValidating}
                    className="btn btn-success text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50"
                  >
                    {isValidating ? '保存中...' : '保存模型'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>



      {/* 密码确认模态窗 */}
      {showPasswordModal && (
        <div
          className="fixed inset-0 z-[70] flex items-center justify-center p-4"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
            backdropFilter: 'blur(8px)',
          }}
          onClick={(e) => {
            e.stopPropagation();
            setShowPasswordModal(false);
            setPasswordInput('');
          }}
        >
          <div
            className="relative bg-white rounded-2xl w-full max-w-sm shadow-2xl border border-gray-100 flex flex-col"
            style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 pb-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {passwordType === 'admin' ? '管理员验证' : '用户验证'}
                </h3>
                <p className="text-sm text-gray-500">
                  {passwordType === 'admin'
                    ? '请输入管理员密码以编辑官方模型配置'
                    : '请输入用户密码以使用官方模型'
                  }
                </p>
              </div>
              <button
                onClick={() => {
                  setShowPasswordModal(false);
                  setPasswordInput('');
                }}
                className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                tabIndex={0}
              >
                <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 内容 */}
            <div className="flex-1 p-6 pt-2 pb-20">
              <input
                type="password"
                placeholder="请输入密码"
                value={passwordInput}
                onChange={(e) => setPasswordInput(e.target.value)}
                className="input input-bordered w-full rounded-xl"
                onKeyDown={(e) => e.key === 'Enter' && handlePasswordConfirm()}
                autoFocus
              />
            </div>

            {/* 底部按钮 */}
            <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
              <button
                onClick={handlePasswordConfirm}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
              >
                确认
              </button>
              <button
                onClick={() => {
                  setShowPasswordModal(false);
                  setPasswordInput('');
                }}
                className="btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIModelManagementModal;
